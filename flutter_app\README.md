# تطبيق M2de Flutter

هذا تطبيق Flutter يحتوي على نفس وظائف تطبيق Dart الأصلي مع واجهة مستخدم جميلة.

## المتطلبات لإنشاء APK:

### 1. تثبيت Flutter:
```bash
# قم بتحميل Flutter من الموقع الرسمي:
# https://docs.flutter.dev/get-started/install/windows

# أو استخدم Git:
git clone https://github.com/flutter/flutter.git -b stable
```

### 2. إعداد متغيرات البيئة:
أضف مسار Flutter إلى PATH في متغيرات البيئة

### 3. تثبيت Android Studio:
- قم بتحميل وتثبيت Android Studio
- تثبيت Android SDK
- إنشاء Android Virtual Device (AVD)

### 4. التحقق من الإعداد:
```bash
flutter doctor
```

## خطوات إنشاء APK:

### 1. الانتقال إلى مجلد المشروع:
```bash
cd flutter_app
```

### 2. تحديث التبعيات:
```bash
flutter pub get
```

### 3. إنشاء APK للإصدار التجريبي:
```bash
flutter build apk --debug
```

### 4. إنشاء APK للإصدار النهائي:
```bash
flutter build apk --release
```

### 5. العثور على ملف APK:
سيتم إنشاء ملف APK في:
```
build/app/outputs/flutter-apk/app-release.apk
```

## ميزات التطبيق:
- عرض رسالة "M2de"
- عداد للنقرات
- عرض التاريخ والوقت الحالي
- واجهة مستخدم جميلة باللغة العربية

## ملاحظات:
- تأكد من تثبيت Flutter بشكل صحيح
- تأكد من إعداد Android SDK
- قد تحتاج لتوقيع التطبيق للنشر في Google Play Store
